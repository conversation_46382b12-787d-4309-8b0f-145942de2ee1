import React from 'react';

const Post: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">文章标题</h1>

          <div className="flex items-center justify-between mb-6 text-sm text-gray-500 border-b border-gray-200 pb-4">
            <span>发布日期：2025年4月5日</span>
            <span>作者：张三</span>
          </div>

          <div className="prose max-w-none py-6">
            <p className="mb-4 text-gray-700">
              这是文章的正文内容。你可以在这里展示完整的文章信息，包括文字、图片和代码示例。
            </p>
            <p className="mb-4 text-gray-700">
              文章的详细内容可以继续扩展，支持 Markdown 格式或者富文本内容。
            </p>
            <img
              src="https://via.placeholder.com/800x400"
              alt="文章配图"
              className="my-6 rounded-lg shadow-sm w-full object-cover"
            />
            <p className="mb-4 text-gray-700">
              这是文章的最后一段内容。你可以根据需要添加评论区或分享按钮。
            </p>
          </div>

          <hr className="my-8 border-gray-200" />

          <div className="flex justify-between items-center mt-8">
            <button className="text-blue-600 hover:text-blue-800 flex items-center py-2 px-4 bg-gray-100 rounded-md transition-colors duration-200">
              ← 上一篇：《另一篇精彩文章》
            </button>
            <button className="text-blue-600 hover:text-blue-800 flex items-center py-2 px-4 bg-gray-100 rounded-md transition-colors duration-200">
              下一篇：《下一篇文章》 →
            </button>
          </div>

          <div className="mt-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">评论区</h2>
            <p className="text-gray-600 mb-6">暂无评论，欢迎发表你的见解。</p>

            <form className="space-y-4">
              <textarea
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-gray-800"
                placeholder="写下你的评论..."
              ></textarea>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                提交评论
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Post;