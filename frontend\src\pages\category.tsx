import React, { useState } from 'react';

const Category: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [selectedTag, setSelectedTag] = useState('全部标签');

  // 模拟文章数据
  const articles = [
    { id: 1, title: '如何开始学习 React', category: '前端开发', tags: ['React', '入门'], date: '2025-04-01' },
    { id: 2, title: 'CSS Grid 布局详解', category: '前端开发', tags: ['CSS', '布局'], date: '2025-03-28' },
    { id: 3, title: 'Node.js 入门指南', category: '后端开发', tags: ['Node.js', '教程'], date: '2025-03-25' },
    { id: 4, title: 'JavaScript 编程技巧', category: '编程基础', tags: ['JavaScript', '技巧'], date: '2025-03-20' },
    { id: 5, title: 'TypeScript 与 JavaScript 的选择', category: '编程基础', tags: ['TypeScript', '对比'], date: '2025-03-18' },
  ];

  // 可选分类和标签
  const categories = ['全部', '前端开发', '后端开发', '编程基础'];
  const tags = ['全部标签', 'React', 'CSS', 'Node.js', 'JavaScript', 'TypeScript', '教程', '入门', '技巧', '对比'];

  // 过滤文章
  const filteredArticles = articles.filter(
    (article) =>
      (selectedCategory === '全部' || article.category === selectedCategory) &&
      (selectedTag === '全部标签' || article.tags.includes(selectedTag))
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">分类与标签浏览</h1>

        {/* 分类与标签筛选 */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <div className="flex flex-col md:flex-row gap-6 mb-4">
            <div className="flex-grow">
              <label htmlFor="category-select" className="block text-sm font-medium text-gray-700 mb-2">
                选择分类
              </label>
              <select
                id="category-select"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex-grow">
              <label htmlFor="tag-select" className="block text-sm font-medium text-gray-700 mb-2">
                选择标签
              </label>
              <select
                id="tag-select"
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {tags.map((tag) => (
                  <option key={tag} value={tag}>
                    {tag}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 文章列表 */}
        <div className="space-y-6">
          {filteredArticles.length > 0 ? (
            filteredArticles.map((article) => (
              <div
                key={article.id}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  <a href={`/post`} className="hover:text-blue-600">
                    {article.title}
                  </a>
                </h2>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                  <span>分类：{article.category}</span>
                  <span>日期：{article.date}</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-6">没有找到匹配的文章。</p>
          )}
        </div>

        {/* 分页 */}
        <div className="flex justify-center mt-8">
          <nav className="inline-flex space-x-2">
            <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
              上一页
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
              1
            </button>
            <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
              下一页
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default Category;