import React from 'react';

const Tutorial: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">教程标题：从零开始学习 React</h1>

          <div className="flex items-center justify-between mb-6 text-sm text-gray-500 border-b border-gray-200 pb-4">
            <span>发布日期：2025年4月5日</span>
            <span>作者：张三</span>
          </div>

          {/* 章节导航 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">本教程包含：</h2>
            <ul className="space-y-2">
              <li>
                <a href="#chapter1" className="text-blue-600 hover:text-blue-800">
                  第一章：React 基础概念
                </a>
              </li>
              <li>
                <a href="#chapter2" className="text-blue-600 hover:text-blue-800">
                  第二章：组件与 Props
                </a>
              </li>
              <li>
                <a href="#chapter3" className="text-blue-600 hover:text-blue-800">
                  第三章：状态管理
                </a>
              </li>
              <li>
                <a href="#chapter4" className="text-blue-600 hover:text-blue-800">
                  第四章：Hooks 使用详解
                </a>
              </li>
            </ul>
          </div>

          <div className="prose max-w-none py-6">
            <h2 id="chapter1" className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              第一章：React 基础概念
            </h2>
            <p className="mb-4 text-gray-700">
              React 是一个用于构建用户界面的 JavaScript 库。它由 Facebook 开发，并被广泛应用于现代前端开发。
            </p>
            <p className="mb-4 text-gray-700">
              在这一章中，我们将介绍 React 的核心概念，包括虚拟 DOM、组件化开发等。
            </p>

            <h2 id="chapter2" className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              第二章：组件与 Props
            </h2>
            <p className="mb-4 text-gray-700">
              组件是 React 应用的基本构建块。你可以将 UI 拆分为独立的、可复用的部分。
            </p>
            <p className="mb-4 text-gray-700">
              Props（属性）允许你向组件传递数据，使组件更加灵活和动态。
            </p>

            <h2 id="chapter3" className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              第三章：状态管理
            </h2>
            <p className="mb-4 text-gray-700">
              状态（State）是 React 中用于存储组件内部数据的机制。通过 useState 和 useEffect，你可以轻松地管理组件的状态。
            </p>
            <p className="mb-4 text-gray-700">
              本章将带你深入了解如何使用状态来构建交互式应用。
            </p>

            <h2 id="chapter4" className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              第四章：Hooks 使用详解
            </h2>
            <p className="mb-4 text-gray-700">
              Hooks 是 React 16.8 引入的新特性，它让你在不编写类组件的情况下使用状态和其他 React 特性。
            </p>
            <p className="mb-4 text-gray-700">
              我们将深入讲解常用的 Hooks 如 useState、useEffect、useContext 等，并展示它们的实际应用场景。
            </p>
          </div>

          <hr className="my-8 border-gray-200" />

          <div className="flex justify-between items-center mt-8">
            <button className="text-blue-600 hover:text-blue-800 flex items-center py-2 px-4 bg-gray-100 rounded-md transition-colors duration-200">
              ← 上一篇：《另一篇精彩教程》
            </button>
            <button className="text-blue-600 hover:text-blue-800 flex items-center py-2 px-4 bg-gray-100 rounded-md transition-colors duration-200">
              下一篇：《下一部教程》 →
            </button>
          </div>

          <div className="mt-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">评论区</h2>
            <p className="text-gray-600 mb-6">暂无评论，欢迎发表你的见解。</p>

            <form className="space-y-4">
              <textarea
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-gray-800"
                placeholder="写下你的评论..."
              ></textarea>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                提交评论
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Tutorial;