import React from 'react';

const Navbar: React.FC = () => {
  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">我的博客</h1>
        <nav className="space-x-4">
          <a href="/" className="text-gray-600 hover:text-gray-900">
            首页
          </a>
          <a href="#" className="text-gray-600 hover:text-gray-900">
            博客
          </a>
          <a href="#" className="text-gray-600 hover:text-gray-900">
            关于
          </a>
          <a href="#" className="text-gray-600 hover:text-gray-900">
            联系
          </a>
        </nav>
      </div>
    </header>
  );
};

export default Navbar;