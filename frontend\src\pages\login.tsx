import { useState } from 'react';

export default function Profile() {
  const [user, setUser] = useState({
    name: '<PERSON>',
    info: { age: 25, city: 'Paris' }
  });

  function handleUpdate() {
    setUser(prev => ({
      ...prev,
      name: '<PERSON>',
      info: {
        ...prev.info,
        city: 'London'
      }
    }));
  }

  return (
      <div>
        <p>姓名: {user.name}</p>
        <p>年龄: {user.info.age}, 城市: {user.info.city}</p>
        <button onClick={handleUpdate}>更新</button>
      </div>
  );
}