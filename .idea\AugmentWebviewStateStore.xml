<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="eyJjdXJyZW50Q29udmVyc2F0aW9uSWQiOiJiZTVlMzE5ZC0xZjBhLTQ0YjItOTljMi00MzliZGQ0OTZiMzAiLCJjb252ZXJzYXRpb25zIjp7IjM5ZGFmNGNjLThlOWMtNGZjZS1hNTdjLTBlMjJkZTkwY2QyYiI6eyJpZCI6IjM5ZGFmNGNjLThlOWMtNGZjZS1hNTdjLTBlMjJkZTkwY2QyYiIsImNyZWF0ZWRBdElzbyI6IjIwMjUtMDQtMTBUMDc6MDA6NDAuOTcwWiIsImxhc3RJbnRlcmFjdGVkQXRJc28iOiIyMDI1LTA0LTEwVDEwOjUzOjUyLjY2OVoiLCJjaGF0SGlzdG9yeSI6W3sic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiIwMTlhODIzOC1lMTljLTQ4NzctODFhNS1lNjM2YzFjNDBmZTYiLCJyZXF1ZXN0X21lc3NhZ2UiOiLluK7miJHkvb/nlKhnb+ivreiogOWIm+W7uuS4gOS4queOsOS7o+WMlueahOaKgOacr+WNmuWuoiIsInJpY2hfdGV4dF9qc29uX3JlcHIiOnsidHlwZSI6ImRvYyIsImNvbnRlbnQiOlt7InR5cGUiOiJwYXJhZ3JhcGgiLCJjb250ZW50IjpbeyJ0eXBlIjoidGV4dCIsInRleHQiOiLluK7miJHkvb/nlKhnb+ivreiogOWIm+W7uuS4gOS4queOsOS7o+WMlueahOaKgOacr+WNmuWuoiJ9XX1dfSwibWVudGlvbmVkX2l0ZW1zIjpbXSwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50Ijoi5oiR5Lya5biu5L2g5Yib5bu65LiA5Liq5L2/55SoR2/or63oqIDnmoTnjrDku6PljJbmioDmnK/ljZrlrqLpobnnm67jgILmiJHku6zlsIbkvb/nlKjku6XkuIvmioDmnK/moIjvvJpcblxuMS4gR2luIOS9nOS4uiBXZWIg5qGG5p62XG4yLiBHT1JNIOS9nOS4uiBPUk0g5qGG5p62XG4zLiBQb3N0Z3JlU1FMIOS9nOS4uuaVsOaNruW6k1xuNC4gSldUIOi/m+ihjOi6q+S7veiupOivgVxuNS4g5YmN56uv5L2/55So5qih5p2/5byV5pOOXG5cbummluWFiO+8jOiuqeaIkeS7rOWIm+W7uumhu<PERSON><PERSON>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" />
      </map>
    </option>
  </component>
</project>