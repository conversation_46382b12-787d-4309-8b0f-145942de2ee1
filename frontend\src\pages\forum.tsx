import React, { useState } from 'react';

const Forum: React.FC = () => {
  const [newPostTitle, setNewPostTitle] = useState('');
  const [newPostContent, setNewPostContent] = useState('');

  // 模拟论坛主题数据
  const forumPosts = [
    {
      id: 1,
      title: 'React Hooks 使用心得',
      author: '用户A',
      replies: 5,
      lastReply: '2025-04-03',
      views: 120,
    },
    {
      id: 2,
      title: '前端性能优化策略',
      author: '用户B',
      replies: 8,
      lastReply: '2025-04-02',
      views: 95,
    },
    {
      id: 3,
      title: 'TypeScript 与 JavaScript 的选择',
      author: '用户C',
      replies: 12,
      lastReply: '2025-04-01',
      views: 200,
    },
  ];

  const handlePostSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 提交新帖子的逻辑（模拟）
    console.log('提交新帖子:', newPostTitle, newPostContent);
    setNewPostTitle('');
    setNewPostContent('');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">论坛讨论区</h1>

        {/* 发帖表单 */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">发表新话题</h2>
          <form onSubmit={handlePostSubmit}>
            <div className="mb-4">
              <label htmlFor="post-title" className="block text-sm font-medium text-gray-700 mb-1">
                标题
              </label>
              <input
                type="text"
                id="post-title"
                value={newPostTitle}
                onChange={(e) => setNewPostTitle(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入帖子标题"
                required
              />
            </div>
            <div className="mb-4">
              <label htmlFor="post-content" className="block text-sm font-medium text-gray-700 mb-1">
                内容
              </label>
              <textarea
                id="post-content"
                rows={4}
                value={newPostContent}
                onChange={(e) => setNewPostContent(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="写下你的想法..."
                required
              ></textarea>
            </div>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              发布帖子
            </button>
          </form>
        </div>

        {/* 论坛帖子列表 */}
        <div className="space-y-4">
          {forumPosts.map((post) => (
            <div
              key={post.id}
              className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                <a href="#" className="hover:text-blue-600">
                  {post.title}
                </a>
              </h2>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>作者：{post.author}</span>
                <span>回复：{post.replies} | 浏览：{post.views} | 最后回复：{post.lastReply}</span>
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        <div className="flex justify-center mt-8">
          <nav className="inline-flex space-x-2">
            <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
              上一页
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
              1
            </button>
            <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
              下一页
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default Forum;