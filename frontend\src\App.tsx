import './App.css';
import {BrowserRouter as Router, Routes, Route} from 'react-router-dom';
import Home from './pages/home';
import Login from './pages/login';
import Post from './pages/post';
import React from "react";
import ArticleList from './pages/article-list';
import Tutorial from './pages/tutorial';
import Forum from './pages/forum';
import Category from './pages/category';
import Profile from './pages/login';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Profile/>}/>
        <Route path="/home" element={<Home/>}/>
        <Route path="/post" element={<Post />} />
        <Route path="/articles" element={<ArticleList />} />
        <Route path="/tutorial" element={<Tutorial />} />
        <Route path="/forum" element={<Forum />} />
        <Route path="/category" element={<Category />} />
      </Routes>
    </Router>
  );
};

export default App;