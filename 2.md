# TVB新聞內容管理系統（CMS）技術架構設計文檔

## 一、總體架構設計

本系統基於 PHP Swoole 協程框架 Hyperf 開發，採用分層架構設計，支持高併發、高可用，並具備良好的可擴展性與維護性。

### 1. 架構圖

```
+-------------------+      +-------------------+      +-------------------+
|   用戶端（Web/移動） |<--->|  API Gateway/負載均衡 |<--->|  Hyperf 應用服務集群 |
+-------------------+      +-------------------+      +-------------------+
                                                        |
                                                        v
                                              +-------------------+
                                              |   MySQL 數據庫集群  |
                                              +-------------------+
                                                        |
                                                        v
                                              +-------------------+
                                              |   Redis 緩存集群    |
                                              +-------------------+
                                                        |
                                                        v
                                              +-------------------+
                                              |   對象存儲（OSS）   |
                                              +-------------------+
```

### 2. 性能目標與容量規劃

- **併發處理能力**：系統設計支持峰值 5,000+ QPS（每秒查詢數）
- **用戶容量**：支持 1,000+ 同時在線編輯用戶，10,000+ 同時在線訪問用戶
- **數據容量**：
  - 新聞條目：每日增量 5,000+ 條，年增長 150 萬+ 條
  - 用戶數據：支持 10,000+ 活躍用戶賬號
  - 媒體存儲：年增長 10TB+ 媒體文件（圖片、視頻等）
- **響應時間**：
  - API 接口響應時間 < 200ms（95% 請求）
  - 頁面加載時間 < 2s（首屏渲染）
  - 後台管理操作響應 < 500ms
- **系統可用性**：99.9%+ 可用性（年計劃停機時間少於 8.76 小時）

## 二、核心技術選型

- **後端框架**：Hyperf 3.0+（基於 Swoole，支持協程，適合高併發場景）
  - 採用協程模式，單機可支持萬級並發連接
  - 內置服務發現、配置中心、熔斷器等微服務組件
  - 支持 AOP、依賴注入，提高代碼復用性與可測試性
  - 自帶連接池（MySQL、Redis等），有效管理資源
- **數據庫**：MySQL 8.0+（主從集群，支持分庫分表）
  - 主從複製架構，一主多從，提高讀性能
  - 使用 ProxySQL 實現讀寫分離與負載均衡
  - 採用 ShardingSphere 實現分庫分表，支持水平擴展
  - 定期優化索引與查詢，監控慢查詢
- **緩存**：Redis 7.0+（哨兵模式集群）
  - 用於 Session、用戶狀態、熱點數據緩存
  - 採用多級緩存策略（本地緩存 + Redis 分布式緩存）
  - 使用 Pipeline 批量操作，提高吞吐量
  - 合理設置 TTL，避免緩存雪崩
- **消息隊列**：Kafka 3.0+（用於異步任務、郵件發送、審計日誌等）
  - 高吞吐量設計，支持百萬級消息處理
  - 消費者組設計，實現負載均衡與故障轉移
  - 消息持久化，避免數據丟失
- **對象存儲**：阿里雲 OSS/MinIO（用於新聞圖片、附件等非結構化數據）
  - CDN 加速分發，提高訪問速度
  - 分層存儲策略，熱數據與冷數據分離
  - 支持數據生命週期管理
- **API Gateway/負載均衡**：Nginx/阿里雲 SLB
  - 實現 API 限流、熔斷、降級
  - SSL 終結與安全防護
  - 智能負載均衡，支持會話保持
- **日誌與監控**：ELK、Prometheus + Grafana
  - 實時日誌收集與分析
  - 系統關鍵指標監控與告警
  - 用戶行為分析與異常檢測
- **容器化與部署**：Docker + Kubernetes（K8S）
  - 容器化部署，環境一致性保證
  - 自動擴縮容，根據負載動態調整資源
  - 滾動更新，實現零停機發布

## 三、模塊劃分

1. **用戶與權限管理模塊**
   - 用戶註冊、登錄、激活、密碼管理
   - 角色與權限分配
   - 操作審計日誌
2. **新聞內容管理模塊**
   - 新聞創建、編輯、審核、發布
   - 內容分級、標籤、分類管理
   - 圖片/附件上傳與管理
3. **審核與工作流模塊**
   - 多級審核流
   - 審核記錄與通知
4. **統計與報表模塊**
   - 用戶行為統計
   - 內容發布統計
5. **系統設置與運維模塊**
   - 配置管理
   - 日誌查詢
   - 系統健康檢查

## 四、性能與安全設計

### 1. 性能設計
- **協程與異步處理**：
  - 使用 Hyperf 協程特性，提升併發處理能力
  - 長耗時操作（如郵件發送、大文件處理）通過異步任務處理
  - 批量操作優化，減少 IO 次數
- **多級緩存策略**：
  - 頁面緩存：對靜態頁面或變化不頻繁的頁面進行全頁緩存
  - 接口緩存：熱門 API 響應緩存，設置合理 TTL
  - 數據緩存：熱點數據緩存至 Redis，減少數據庫壓力
  - 本地緩存：極高頻訪問的配置數據使用本地緩存
- **數據庫優化**：
  - 讀寫分離，主庫寫入，從庫讀取
  - 合理設計索引，定期分析與優化查詢
  - 大表分庫分表，歷史數據歸檔
  - 避免慢查詢，設置查詢超時監控
- **靜態資源優化**：
  - 靜態資源與大文件通過對象存儲分流
  - CDN 加速分發，就近訪問
  - 資源壓縮與合併，減少請求數
  - 圖片自適應壓縮與格式優化
- **擴展性設計**：
  - API Gateway 實現請求限流與負載均衡
  - 服務無狀態化設計，支持橫向擴展
  - 服務可動態擴容，根據負載自動調節實例數

### 2. 安全設計
- **身份認證與授權**：
  - 用戶密碼加密存儲（bcrypt 算法），敏感信息脫敏
  - 所有接口需權限校驗，支持 RBAC 精細化權限控制
  - JWT 令牌管理，支持令牌過期與撤銷
  - 關鍵操作需二次認證
- **數據安全**：
  - 敏感操作二次確認，並記錄審計日誌
  - 數據庫定期備份，支持時間點恢復
  - 數據傳輸加密，API 通信使用 HTTPS
  - 數據訪問控制，按角色設置數據訪問範圍
- **應用安全**：
  - 防止 SQL 注入、XSS、CSRF 等常見攻擊
  - 輸入驗證與過濾，防止惡意輸入
  - API 限流與防爬蟲措施
  - WAF（Web 應用防火牆）防護
- **運維安全**：
  - 支持 HTTPS，數據傳輸加密
  - 支持登錄異地提醒與風險控制
  - 服務器加固，最小權限原則
  - 定期安全掃描與漏洞修復

## 五、部署方案

- **環境隔離**：
  - **開發/測試/生產環境隔離**，配置多套部署
  - 環境間數據隔離，避免交叉污染
  - 生產環境多可用區部署，提高可用性
- **容器化部署**：
  - **Docker 容器化**，支持一鍵部署與回滾
  - 鏡像版本管理，支持快速回滾
  - 統一環境配置，避免環境差異問題
- **服務編排**：
  - **Kubernetes 編排**，自動擴縮容、故障自愈
  - 服務健康檢查與自動恢復
  - 滾動更新策略，實現零停機部署
- **持續集成/交付**：
  - **CI/CD 持續集成**，自動化測試與部署
  - 代碼質量檢查與單元測試自動化
  - 發布流程標準化，減少人為錯誤
- **數據備份**：
  - **數據庫主從備份與定期快照**，確保數據安全
  - 增量備份與全量備份結合，提高備份效率
  - 自動化備份策略，定期驗證備份有效性
- **災備方案**：
  - **多地區容災備份**，提升系統可用性
  - 同城雙活或異地災備，根據業務需求選擇
  - 定期災備演練，確保災備方案有效

## 六、可維護性與擴展性

- **代碼組織**：
  - 採用分層架構（控制器、服務、數據訪問、實體、DTO）
  - 遵循 PSR 規範，提高代碼一致性
  - 統一編碼規範與命名約定
- **業務封裝**：
  - 所有業務邏輯通過服務層封裝，便於單元測試與複用
  - 領域驅動設計思想，業務邏輯與技術實現分離
  - 接口與實現分離，提高系統靈活性
- **擴展機制**：
  - 支持插件化開發，便於新功能擴展
  - 事件驅動設計，支持業務解耦
  - 服務發現機制，支持動態服務擴展
- **文檔與規範**：
  - 完善的接口文檔與代碼註釋，提升可讀性
  - API 文檔自動生成（Swagger/OpenAPI）
  - 系統架構圖與業務流程圖維護更新

## 七、各模塊技術實現細化（已根據需求文檔優化）

### 1. 用戶與權限管理模塊
- **核心子模塊**：用戶註冊/登錄、用戶信息管理、角色管理、權限管理、審計日誌、郵件服務
- **關鍵技術點**：
  - 使用JWT進行用戶認證，Token存儲於Redis實現單點登錄與會話管理。登出或停用用戶時，可通過將 Redis 中的 JWT 加入黑名單或直接刪除來實現會話立即失效。
  - RBAC（基於角色的權限控制）模型，權限粒度細化到接口/操作級。
  - 密碼加密存儲（如bcrypt）。關鍵管理操作（如停用用戶、重置密碼）應增加二次確認機制，例如要求重新输入密碼。
  - 用戶賬戶包含多種狀態（`未激活`、`已激活`、`已停用`），狀態變更將觸發相應的業務邏輯（如郵件發送、權限變更）。
  - 管理員修改用戶郵箱後，用戶狀態將自動變為`未激活`，並觸發新的激活郵件發送流程。
  - 操作審計日誌記錄到消息隊列，異步寫入ElasticSearch。
  - 郵件發送通過消息隊列異步處理，提升響應速度。
- **主要接口**：
  - `POST /api/auth/login` - 用戶登錄
  - `POST /api/auth/logout` - 用戶登出
  - `POST /api/auth/activate/{token}` - 用戶激活賬號
  - `POST /api/admin/users` - 管理員創建用戶
  - `GET /api/admin/users` - 管理員獲取用戶列表
  - `PUT /api/admin/users/{id}` - 管理員更新用戶信息 (註：不包括用戶自己才能修改的字段，如笔名)
  - `PATCH /api/admin/users/{id}/status` - 管理員變更用戶狀態 (激活/停用)
  - `POST /api/admin/users/{id}/resend-activation` - 管理員重發激活郵件
  - `GET /api/me/profile` - 用戶獲取個人資料
  - `PUT /api/me/profile` - 用戶更新個人資料 (如笔名)
  - `GET /api/admin/roles` - 獲取角色列表
  - `POST /api/admin/roles` - 創建/更新角色
- **數據流**：
  - 用戶操作→API Gateway→Hyperf服務→MySQL/Redis→消息隊列→郵件/審計服務
- **設計模式**：
  - 單例模式（用於Redis連接池）
  - 工廠模式（用於郵件/消息發送）
  - 策略模式（用於權限校驗）

### 2. 新聞內容管理模塊
- **核心子模塊**：新聞創建/編輯、內容審核、分類/標籤管理、附件管理、內容查詢
- **關鍵技術點**：
  - 富文本編輯器集成，支持多媒體內容。
  - 新聞內容應支持版本控制（歷史版本追溯），防止誤操作導致內容丟失。
  - 內容審核流與狀態機設計，支持多級審核。
  - 圖片/附件上傳至對象存儲，返回URL存入數據庫。
  - 熱門新聞、最新新聞等列表緩存至Redis。
  - 使用`Elasticsearch`進行全文檢索，配置中文分詞器（如`IK Analyzer`）以優化中文內容的搜索準確性。索引字段包括標題、正文、作者、標籤等。
- **主要接口**：
  - POST /api/news
  - PUT /api/news/{id}
  - GET /api/news
  - POST /api/news/upload
  - GET /api/categories
- **數據流**：
  - 編輯/上傳→API Gateway→Hyperf服務→MySQL/OSS/ElasticSearch→Redis
- **設計模式**：
  - 狀態模式（新聞審核流）
  - 裝飾器模式（內容過濾、審核）
  - 觀察者模式（內容發布通知）

### 3. 審核與工作流模塊
- **核心子模塊**：審核流設計、審核記錄、通知推送、審核權限
- **關鍵技術點**：
  - 可基於狀態機模式（State Pattern）或引入現成的PHP工作流引擎庫（如`symfony/workflow`）來實現，將審核流程配置化，支持可視化編輯則更佳。
  - 審核記錄持久化，支持追溯。
  - 通知推送（站內信/郵件/短信）通過消息隊列異步處理。
- **主要接口**：
  - POST /api/audit/submit
  - GET /api/audit/records
  - POST /api/audit/approve
- **數據流**：
  - 審核操作→API Gateway→Hyperf服務→MySQL/消息隊列→通知服務
- **設計模式**：
  - 責任鏈模式（多級審核流）
  - 觀察者模式（審核結果通知）

### 4. 統計與報表模塊
- **核心子模塊**：用戶行為統計、內容統計、報表導出
- **關鍵技術點**：
  - 用戶行為數據通過埋點上報，Kafka異步收集。
  - 統計數據存儲於ElasticSearch/ClickHouse，支持高效查詢。
  - 報表導出功能可使用`phpspreadsheet`生成Excel，使用`TCPDF`或`Dompdf`生成PDF。對於大數據量導出，應採用異步任務處理，生成文件後提供下載鏈接或發送郵件通知。
- **主要接口**：
  - GET /api/stat/user
  - GET /api/stat/news
  - GET /api/stat/export
- **數據流**：
  - 行為上報→消息隊列→統計服務→ElasticSearch/ClickHouse→API查詢
- **設計模式**：
  - 策略模式（不同統計口徑）
  - 工廠模式（報表導出格式）

### 5. 系統設置與運維模塊
- **核心子模塊**：配置管理、日誌查詢、健康檢查、權限審計
- **關鍵技術點**：
  - 配置中心設計，支持動態刷新（如Apollo/Nacos）
  - 日誌統一收集至ELK，支持多維查詢
  - 健康檢查接口，K8S自動探針
  - 系統審計與告警，異常自動通知
- **主要接口**：
  - GET /api/config
  - PUT /api/config
  - GET /api/logs
  - GET /api/health
- **數據流**：
  - 配置/查詢→API Gateway→Hyperf服務→配置中心/ELK/Prometheus
- **設計模式**：
  - 單例模式（配置中心客戶端）
  - 觀察者模式（配置變更通知）

## 八、性能優化與擴展策略

### 1. 應用層優化
- **協程池管理**：合理設置協程數量，避免資源過度使用
- **連接池優化**：MySQL、Redis等連接池參數調優，避免連接瓶頸
- **請求合併與批處理**：減少網絡往返，提高吞吐量
- **異步處理**：非核心流程異步化，提高主流程響應速度
- **熱點代碼優化**：識別並優化熱點代碼路徑，減少CPU消耗

### 2. 數據層優化
- **分庫分表策略**：根據業務增長預測，提前規劃分庫分表方案
- **索引優化**：定期分析查詢模式，優化索引設計
- **SQL優化**：避免全表掃描，優化JOIN策略，合理使用子查詢
- **數據歸檔**：歷史數據定期歸檔，保持活躍數據表高效

### 3. 緩存策略
- **多級緩存**：應用層緩存 + 分布式緩存，降低數據庫負載
- **預熱機制**：系統啟動或發布後主動預熱熱點數據
- **緩存更新策略**：根據數據特性選擇更新策略（TTL過期/主動更新/雙寫一致性）
- **緩存穿透防護**：空值緩存、布隆過濾器等機制

### 4. 擴展策略
- **水平擴展**：服務無狀態化設計，支持按需擴容
- **讀寫分離**：讀多寫少場景下，增加只讀節點提升查詢性能
- **微服務拆分**：高負載模塊獨立部署，單獨擴容
- **彈性伸縮**：根據負載指標（CPU/內存/QPS）自動調整實例數

## 九、監控與預警體系

### 1. 系統監控
- **基礎設施監控**：服務器CPU、內存、磁盤、網絡等資源使用率
- **應用監控**：請求量、響應時間、錯誤率、JVM狀態等
- **數據庫監控**：連接數、查詢性能、鎖等待、慢查詢等
- **緩存監控**：命中率、過期率、內存使用等

### 2. 業務監控
- **用戶行為**：登錄頻率、頁面訪問、功能使用等
- **內容發布**：發布量、審核時間、內容分類分布等
- **系統使用**：峰值時段、用戶地域分布、設備類型等

### 3. 告警機制
- **多級告警**：按嚴重程度分級（通知、警告、嚴重、緊急）
- **智能閾值**：動態閾值，避免誤報
- **告警渠道**：郵件、短信、企業微信/釘釘等多渠道通知
- **告警抑制**：避免告警風暴，合併同類告警

## 十、數據遷移與系統切換方案

為確保從現有系統平滑過渡到新系統，同時保證 TVB 業務正常運行不受影響，本章節詳細規劃數據遷移與系統切換策略。

### 1. 數據遷移總體策略

- **分階段遷移**：按照數據重要性和依賴關係，將數據分為基礎數據、業務數據、歷史數據等多個階段進行遷移
- **雙寫機制**：在過渡期實施新舊系統數據雙寫，確保數據一致性
- **增量同步**：使用 CDC（變更數據捕獲）工具實現數據實時同步
- **數據校驗**：每個階段結束後進行數據完整性和一致性校驗
- **回滾機制**：制定詳細的回滾計劃，確保問題發生時能快速恢復

### 2. MySQL 數據庫遷移

- **前期準備**：
  - 梳理數據表結構，建立新舊系統表映射關係
  - 設計數據轉換規則，處理字段類型、命名規範等差異
  - 準備足夠的存儲空間和網絡帶寬，評估遷移時間窗口

- **基礎數據遷移**：
  - 使用 `mysqldump` 或專業數據遷移工具（如 AWS DMS、阿里雲 DTS）導出基礎配置數據
  - 進行數據清洗和轉換，適配新系統結構
  - 導入新系統並進行數據校驗

- **增量數據同步**：
  - 部署 Canal、DTS 等 CDC 工具捕獲數據變更
  - 實時將變更同步至新系統，保持數據一致性
  - 監控同步延遲和錯誤率，確保數據可靠性

- **性能優化**：
  - 遷移過程中關閉非必要索引，遷移完成後重建
  - 批量導入數據，合理設置事務大小
  - 適當調整 MySQL 參數（如 `innodb_buffer_pool_size`）提升導入效率

### 3. ElasticSearch 數據遷移

- **索引重建策略**：
  - 分析現有 ES 索引結構，優化新系統索引設計
  - 使用 Logstash 或 ES Reindex API 進行數據遷移
  - 針對中文內容，配置適合的分詞器（如 IK Analyzer）

- **增量索引更新**：
  - 利用 ES 的 Pipeline 或 Logstash 進行數據轉換和增量更新
  - 設置數據同步任務，定期從數據庫更新到 ES
  - 監控索引效率和查詢性能，適時優化

- **索引別名機制**：
  - 使用 ES 索引別名（Index Alias）機制實現無縫切換
  - 新索引建立並數據同步完成後，切換索引別名指向
  - 應用程序始終訪問別名，無需修改配置

### 4. 媒體文件遷移

- **分批次遷移策略**：
  - 按照時間順序或業務重要性分批遷移媒體文件
  - 使用專業數據傳輸工具（如 rsync、rclone）保證傳輸效率和完整性
  - 保留原始文件路徑結構或建立映射關係

- **對象存儲遷移**：
  - 如果新舊系統都使用對象存儲，可使用存儲服務提供的跨桶複製功能
  - 對於從傳統文件系統遷移到對象存儲，使用批量上傳工具
  - 建立 CDN 預熱機制，確保熱門資源快速訪問

- **文件去重與優化**：
  - 遷移過程中進行文件去重，節省存儲空間
  - 根據訪問頻率設置存儲分層策略（熱/溫/冷數據）
  - 優化圖片格式和質量，提升加載速度

### 5. 系統切換策略

- **灰度發布**：
  - 選擇少量非核心業務先行切換，驗證系統穩定性
  - 逐步擴大切換範圍，密切監控系統性能和錯誤率
  - 制定詳細的灰度規則和回滾機制

- **流量切換**：
  - 使用 API Gateway 或負載均衡器實現流量精準控制
  - 按照用戶群體、地域、業務類型等維度進行流量分配
  - 支持按比例切換和快速回滾能力

- **雙系統並行運行**：
  - 新舊系統並行運行一段時間（建議 2-4 週）
  - 實施數據雙寫，確保數據一致性
  - 設置監控對比，及時發現並解決問題

- **應急預案**：
  - 制定詳細的應急響應流程和角色分工
  - 預設多個回滾檢查點，支持快速回滾
  - 準備足夠的技術支持人員，保障切換過程順暢

### 6. 數據驗證與質量保證

- **自動化驗證**：
  - 開發數據一致性校驗工具，自動比對新舊系統數據
  - 設置關鍵業務指標監控，確保功能正常
  - 建立數據遷移日誌和審計機制

- **人工抽檢**：
  - 制定抽樣方案，對重要數據進行人工核對
  - 關鍵業務流程進行端到端測試
  - 收集用戶反饋，及時調整優化

- **性能基準測試**：
  - 在切換前進行全面的性能測試
  - 模擬峰值負載，驗證系統穩定性
  - 與舊系統性能對比，確保用戶體驗提升

### 7. 遷移時間表與風險控制

- **時間規劃**：
  - 準備階段：4-6 週（環境準備、遷移工具開發、測試環境驗證）
  - 基礎數據遷移：1-2 週（配置數據、用戶數據等）
  - 業務數據遷移：2-4 週（根據數據量調整）
  - 系統並行運行：2-4 週
  - 全面切換：分階段進行，總計 1-2 週

- **風險識別與控制**：
  - 識別關鍵風險點：數據丟失、系統不穩定、性能下降等
  - 每個風險點制定具體應對措施和責任人
  - 建立決策機制，明確何時暫停或回滾的標準

- **溝通與培訓**：
  - 提前與各利益相關方溝通遷移計劃
  - 對運維和支持人員進行新系統培訓
  - 準備用戶指南，幫助用戶快速適應新系統

### 8. 遷移後運維與優化

- **密切監控**：
  - 切換後加強監控頻率和深度，及時發現問題
  - 設置更嚴格的告警閾值，提高響應速度
  - 分析系統日誌，識別潛在問題

- **性能調優**：
  - 根據實際運行數據，優化數據庫索引和查詢
  - 調整緩存策略，提高命中率
  - 優化代碼熱點，提升系統響應速度

- **經驗總結**：
  - 記錄遷移過程中的問題和解決方案
  - 評估遷移策略的有效性，提出改進建議
  - 形成遷移經驗文檔，為未來項目提供參考

---

> 各模塊技術實現細化，便於後續分工開發與架構優化。每個模塊均考慮高併發、可維護性與安全性，並結合業界最佳實踐。

> 本文檔為 TVB 新聞內容管理系統（CMS）技術架構設計說明，後續可根據實際需求進行細化與調整。 