•視頻上傳需要格式驗證和病毒掃描
•HTML/JavaScript代碼插入需要特殊權限
•社交媒體嵌入需要驗證URL合法性
•ChatGPT API調用需要API密鑰權限
•所有用戶操作都記錄在編輯歷史中
1.5 業務規則
•文章標題不能為空且不超過150字符
•文章必須包含至少一個內容模塊
•圖片支持格式：jpg, jpeg, png, webp, tiff, heic
•視頻支持格式：mp4, mov, avi, wmv，最大500MB
•社交媒體URL必須來自支持的平台
•模塊顯示/隱藏狀態影響前端展示但不影響內容保存
•水印只能選擇一個位置，默認無水印
•批量圖片上傳支持多選和拖拽
•AI摘要以要點形式呈現
Tab 2: SEO
2.1 功能描述
提供SEO優化功能，包括AI自動生成SEO元素、社交媒體分享優化和翻譯功能，提升文章的搜索引擎表現和社交媒體傳播效果。
2.2 字段描述

点击图片可查看完整电子表格
2.3 功能使用流程
SEO自動生成流程:
1.用戶點擊"一鍵生成SEO"按鈕
2.系統分析文章標題和內容
3.調用ChatGPT API生成SEO標題、描述和關鍵字
4.基於SEO標題自動生成URL Slug
5.用戶可手動編輯任何自動生成的內容
社交媒體優化流程:
1.系統自動填充Open Graph標籤默認值
2.og:title默認使用文章標題
3.og:description默認使用SEO描述
4.og:image默認使用文章封面圖片
5.用戶可手動自定義任何字段

2.4 安全和權限控制
•ChatGPT API調用需要API密鑰權限
•URL Slug生成需要檢查唯一性
•發佈後URL Slug不可修改
•翻譯功能需要詞彙庫訪問權限
•社交分享圖片需要尺寸驗證
2.5 業務規則
•SEO標題建議60字符以內
•SEO描述建議160字符以內
•URL Slug只能包含小寫英文、數字和連字符
•URL Slug必須唯一，重複時自動添加數字後綴
•發佈後URL Slug鎖定，刪除內容時設置301重定向
•翻譯基於香港-大陸詞彙對照庫
•Open Graph圖片建議1200x630像素
Tab 3: 編輯記錄
3.1 功能描述
顯示文章的完整編輯歷史記錄，包括所有操作的詳細信息、搜索功能、導出功能和歸檔管理。
3.2 字段描述

点击图片可查看完整电子表格
3.3 功能使用流程
查看編輯記錄流程:
a.用戶點擊"編輯記錄"標籤
b.系統顯示所有操作記錄，按時間倒序排列
c.每條記錄顯示時間、用戶、操作類型、操作詳情
d.支持分頁瀏覽歷史記錄
搜索記錄流程:
a.用戶在搜索框輸入條件
b.可按時間範圍、用戶、操作類型篩選
c.支持關鍵字搜索操作內容
d.顯示篩選後的結果列表
導出記錄流程:
a.用戶點擊"導出記錄"按鈕
b.選擇導出時間範圍和篩選條件
c.系統生成PDF格式的編輯記錄報告
d.提供下載鏈接供用戶下載
3.4 安全和權限控制
￮編輯記錄僅有閱讀權限，不可修改或刪除
￮敏感操作記錄需要特殊權限查看
￮導出功能需要報告生成權限
￮記錄數據需要定期備份
3.5 業務規則
￮所有用戶操作都必須記錄，包括字段級別的變更
￮記錄內容包括操作前後的值對比
￮系統操作（如自動保存）需要標識為系統操作
￮編輯記錄每3個月自動歸檔壓縮
￮歸檔記錄仍可查詢但載入速度較慢
￮記錄保留期限為5年，到期後物理刪除

## 技术架构设计

### 1. 总体架构概述
本系统采用分层架构设计，核心后端基于 Hyperf 框架（Swoole 协程驱动），前端采用现代化 SPA 技术栈，支持高并发、可扩展与高可用。

- 前端：React/Vue3 + TypeScript + Vite，组件化开发，接口与后端解耦。
- 后端：Hyperf（PHP 8+），基于 Swoole 协程，支持异步非阻塞 I/O。
- 数据库：MySQL 8+，主从分离，读写分离，支持分库分表。
- 缓存：Redis，支持高并发缓存与分布式锁。
- 消息队列：基于 Redis/AMQP 实现异步任务与事件驱动。
- API 网关：统一鉴权、限流、日志追踪。
- 微服务：支持服务注册与发现，采用 RPC/HTTP 通信。
- 部署：Docker 容器化，K8s 编排，支持弹性伸缩。

### 2. 核心技术选型与理由
- Hyperf 框架：高性能协程，丰富组件，适合微服务与高并发场景。
- Swoole：提供异步、协程、连接池等能力，极大提升 PHP 性能。
- Redis：缓存热点数据、分布式锁、队列，提升系统吞吐量。
- MySQL：关系型数据存储，支持事务与复杂查询。
- Docker/K8s：实现弹性伸缩与自动化运维。

### 3. 性能优化与高并发方案
- 连接池：数据库与 Redis 连接池，减少连接创建销毁开销。
- 协程：充分利用 Swoole 协程并发处理请求，提升 QPS。
- 缓存策略：热点数据、会话、鉴权信息全部缓存至 Redis，降低 DB 压力。
- 队列与异步：耗时操作（如邮件、日志、导出）全部异步处理。
- 分布式锁：保证高并发下的数据一致性。
- 服务拆分：微服务架构，按业务域拆分，独立部署与扩展。
- 限流与熔断：API 网关统一限流，防止雪崩。

### 4. 安全与可维护性设计
- 严格输入校验与输出过滤，防止 SQL 注入/XSS。
- 敏感操作二次确认，操作日志全量记录。
- 配置中心与服务注册中心，支持动态扩容与灰度发布。
- 代码分层、模块化，单文件不超300行，便于维护。

---

当前进度：3/3，技术架构文档已追加至1.md文件末尾，如需详细架构图或具体业务流程可进一步补充。

